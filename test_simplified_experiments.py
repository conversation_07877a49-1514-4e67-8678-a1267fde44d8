#!/usr/bin/env python3
"""
Test script to verify simplified experiment fixes work correctly
"""

import os
import subprocess
import json
import time
from datetime import datetime

def test_single_experiment(dataset, model, pred_len, method):
    """Test a single experiment configuration"""
    
    print(f"Testing: {dataset} - {model} - {pred_len}h - {method}")
    start_time = time.time()
    
    # Set model-specific parameters
    if model == "PatchTST":
        d_model, n_heads, e_layers, d_ff = 16, 4, 3, 128
    elif model == "iTransformer":
        d_model, n_heads, e_layers, d_ff = 256, 8, 2, 256
    else:  # Default for others
        d_model, n_heads, e_layers, d_ff = 512, 8, 2, 2048
    
    # Base command
    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", str(pred_len),
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", "1",  # Reduced for testing
        "--itr", "1",
        "--features", "M",
        "--d_model", str(d_model),
        "--n_heads", str(n_heads),
        "--e_layers", str(e_layers),
        "--d_ff", str(d_ff)
    ]
    
    # Add method-specific parameters
    if method == "Offline":
        # Just train and test
        pass
    elif method == "FSNet":
        # Test FSNet with CPU support
        cmd.extend([
            "--online_learning_rate", "0.00003",
            "--online_method", "FSNet",
            "--border_type", "online",
            "--save_opt",
            "--only_test"
        ])
    elif method == "ClearE":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "ClearE",
            "--concept_dim", "64",
            "--bottleneck_dim", "32",
            "--metadata_dim", "10",
            "--metadata_hidden_dim", "32",
            "--drift_memory_size", "10",
            "--drift_reg_weight", "0.1",
            "--use_energy_loss",
            "--high_load_threshold", "0.8",
            "--underestimate_penalty", "2.0",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test",
            "--val_online_lr",
            "--diff_online_lr",
            "--tune_mode", "down_up"
        ])
        
        # Apply checkpoint path fix for ClearE
        import shutil
        
        # Get model-specific parameters for checkpoint path
        if model == "PatchTST":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm16_nh4_el3_dl1_df128_fc3_ebtimeF_dtTrue_test_0"
        elif model == "iTransformer":
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm256_nh8_el2_dl1_df256_fc3_ebtimeF_dtTrue_test_0"
        else:
            source_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            target_path = f"./checkpoints/{dataset}_96_{pred_len}_{model}_online_ftM_sl96_ll48_pl{pred_len}_lr0.0001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0"
            
        # Create target directory and copy checkpoint if source exists
        os.makedirs(target_path, exist_ok=True)
        source_checkpoint = f"{source_path}/checkpoint.pth"
        target_checkpoint = f"{target_path}/checkpoint.pth"
        
        if os.path.exists(source_checkpoint) and not os.path.exists(target_checkpoint):
            print(f"Copying checkpoint from {source_checkpoint} to {target_checkpoint}")
            try:
                shutil.copy2(source_checkpoint, target_checkpoint)
                print(f"Successfully copied checkpoint")
            except Exception as e:
                print(f"Error copying checkpoint: {e}")
        elif not os.path.exists(source_checkpoint):
            print(f"Warning: Source checkpoint not found: {source_checkpoint}")
        else:
            print(f"Target checkpoint already exists: {target_checkpoint}")
    
    # Run the experiment
    try:
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10 min timeout for testing
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✓ Test passed for {dataset}-{model}-{pred_len}h-{method}")
            print(f"  Duration: {duration:.1f}s")
            return True, None
        else:
            print(f"✗ Test failed for {dataset}-{model}-{pred_len}h-{method}")
            print(f"  Error: {result.stderr}")
            print(f"  Duration: {duration:.1f}s")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"✗ Test timed out for {dataset}-{model}-{pred_len}h-{method}")
        return False, "Timeout"
    except Exception as e:
        print(f"✗ Test exception for {dataset}-{model}-{pred_len}h-{method}: {e}")
        return False, str(e)

def main():
    print("=== Testing Simplified Experiment Configuration ===")
    
    # Test configurations - simplified working methods only
    test_configs = [
        # Test basic offline methods
        ("ETTh1", "PatchTST", 24, "Offline"),
        ("ETTh1", "iTransformer", 24, "Offline"),
        
        # Test FSNet with CPU support
        ("ETTh1", "PatchTST", 24, "FSNet"),
        ("ETTh1", "iTransformer", 24, "FSNet"),
        
        # Test ClearE method
        ("ETTh1", "PatchTST", 24, "ClearE"),
        ("ETTh1", "iTransformer", 24, "ClearE"),
    ]
    
    print(f"Running {len(test_configs)} test configurations...")
    
    results = []
    passed = 0
    failed = 0
    
    for i, (dataset, model, pred_len, method) in enumerate(test_configs, 1):
        print(f"\n--- Test {i}/{len(test_configs)} ---")
        success, error = test_single_experiment(dataset, model, pred_len, method)
        
        results.append({
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': success,
            'error': error
        })
        
        if success:
            passed += 1
        else:
            failed += 1
    
    # Summary
    print(f"\n=== Test Summary ===")
    print(f"Total tests: {len(test_configs)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success rate: {passed/len(test_configs)*100:.1f}%")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs("logs/test_results", exist_ok=True)
    results_file = f"logs/test_results/simplified_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'total_tests': len(test_configs),
            'passed': passed,
            'failed': failed,
            'results': results
        }, f, indent=2)
    
    print(f"Test results saved to: {results_file}")
    
    # Print failed tests for debugging
    if failed > 0:
        print(f"\n=== Failed Tests ===")
        for result in results:
            if not result['success']:
                print(f"- {result['dataset']}-{result['model']}-{result['pred_len']}h-{result['method']}: {result['error']}")
    
    return passed == len(test_configs)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
