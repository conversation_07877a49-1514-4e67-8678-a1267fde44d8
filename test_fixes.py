#!/usr/bin/env python3
"""
Test script to validate experimental setup fixes
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_provider.data_factory import data_provider
from settings import data_settings, pretrain_lr_online_dict, pretrain_lr_dict

def test_dataset_loading():
    """Test that all datasets can be loaded"""
    print("=== Testing Dataset Loading ===")
    datasets = ['ECL', 'gefcom2014', 'southern_china']
    
    class Args:
        def __init__(self, dataset_name):
            config = data_settings[dataset_name]
            self.data = dataset_name
            self.dataset = dataset_name
            self.data_path = config['data']
            self.root_path = './dataset/'
            self.features = 'M'
            self.target = config['T']
            self.seq_len = 96
            self.label_len = 48
            self.pred_len = 24
            self.timeenc = 1
            self.freq = 'h'
            self.train_only = False
            self.pin_gpu = False
            self.wrap_data_class = []
            self.model = 'PatchTST'
            self.embed = 'timeF'
            self.use_time = False
            self.batch_size = 16
            self.num_workers = 0
            self.local_rank = -1
            self.borders = None
    
    for dataset in datasets:
        try:
            args = Args(dataset)
            train_data, train_loader = data_provider(args, flag='train')
            print(f"✓ {dataset}: {len(train_data)} samples")
        except Exception as e:
            print(f"❌ {dataset}: {str(e)}")

def test_learning_rate_coverage():
    """Test that all models have learning rates for all datasets"""
    print("\n=== Testing Learning Rate Coverage ===")
    
    datasets = ['ETTh1', 'ETTh2', 'ETTm1', 'ETTm2', 'ECL', 'gefcom2014', 'southern_china']
    models = ['PatchTST', 'iTransformer', 'DLinear', 'Linear', 'NLinear', 'RLinear']
    
    missing_configs = []
    for model in models:
        if model in pretrain_lr_online_dict:
            for dataset in datasets:
                if dataset not in pretrain_lr_online_dict[model]:
                    missing_configs.append(f"{model}-{dataset}")
    
    if missing_configs:
        print(f"❌ Missing configurations: {missing_configs}")
    else:
        print("✓ All model-dataset combinations have learning rates")

if __name__ == "__main__":
    test_dataset_loading()
    test_learning_rate_coverage()
    print("\n=== Test Complete ===")
