#!/usr/bin/env python3
"""
Monitor comprehensive journal experiments progress
"""

import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

def check_job_status(job_id):
    """Check the status of the submitted job"""
    try:
        result = subprocess.run(['bjobs', str(job_id)], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                status_line = lines[1].split()
                status = status_line[2] if len(status_line) > 2 else "UNKNOWN"
                return status
        return "NOT_FOUND"
    except Exception:
        return "ERROR"

def check_intermediate_results():
    """Check for intermediate results files"""
    results_dir = Path("results/comprehensive_journal_experiments")
    if not results_dir.exists():
        return None
    
    # Find latest intermediate results
    intermediate_files = list(results_dir.glob("intermediate_results_*.json"))
    if not intermediate_files:
        return None
    
    latest_file = max(intermediate_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        return data
    except:
        return None

def check_final_results():
    """Check for final results"""
    results_dir = Path("results/comprehensive_journal_experiments")
    if not results_dir.exists():
        return None
    
    final_files = list(results_dir.glob("final_comprehensive_results_*.json"))
    if not final_files:
        return None
    
    latest_file = max(final_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        return data
    except:
        return None

def monitor_experiments(job_id, check_interval=300):  # Check every 5 minutes
    """Monitor the comprehensive journal experiments"""
    
    print(f"🔍 MONITORING COMPREHENSIVE JOURNAL EXPERIMENTS")
    print(f"Job ID: {job_id}")
    print(f"Check interval: {check_interval} seconds")
    print("=" * 60)
    
    start_time = time.time()
    last_status = None
    last_progress = None
    
    while True:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        elapsed_hours = (time.time() - start_time) / 3600
        
        # Check job status
        job_status = check_job_status(job_id)
        
        # Check for results
        intermediate_data = check_intermediate_results()
        final_data = check_final_results()
        
        print(f"\n[{current_time}] Status Update (Elapsed: {elapsed_hours:.1f}h)")
        print("-" * 50)
        
        # Job status
        if job_status != last_status:
            print(f"Job Status: {job_status}")
            last_status = job_status
        
        # Progress from intermediate results
        if intermediate_data:
            progress = f"{intermediate_data['completed_experiments']}/{intermediate_data['total_experiments']}"
            success_rate = intermediate_data['success_rate']
            
            if progress != last_progress:
                print(f"Progress: {progress} ({success_rate:.1f}% success rate)")
                print(f"Successful: {intermediate_data['successful_experiments']}")
                print(f"Failed: {intermediate_data['failed_experiments']}")
                last_progress = progress
        
        # Check if completed
        if final_data:
            print("\n🎉 EXPERIMENTS COMPLETED!")
            print(f"Final Success Rate: {final_data['success_rate']:.1f}%")
            print(f"Total Successful: {final_data['successful_experiments']}")
            print(f"Total Duration: {final_data['total_duration']/3600:.1f} hours")
            
            if final_data['success_rate'] >= 90:
                print("✅ SUCCESS: >90% success rate achieved!")
                print("Ready for journal paper integration.")
                print("\nNext steps:")
                print("1. Run: python integrate_journal_results.py")
                print("2. Review updated paper/paper.tex")
                print("3. Submit to high-impact journal")
            else:
                print(f"⚠️  WARNING: {final_data['success_rate']:.1f}% success rate")
                print("May need additional debugging for optimal results.")
            
            break
        
        # Check if job failed
        if job_status in ["EXIT", "ZOMBI"]:
            print(f"❌ Job failed with status: {job_status}")
            print("Check error logs:")
            print(f"  tail -f logs/comprehensive_journal_{job_id}.err")
            break
        
        # Check if job is done but no results
        if job_status == "DONE" and not final_data:
            print("⚠️  Job completed but no final results found")
            print("Check output logs for issues:")
            print(f"  tail -f logs/comprehensive_journal_{job_id}.out")
            break
        
        # Wait before next check
        time.sleep(check_interval)

def main():
    """Main monitoring function"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python monitor_journal_experiments.py <job_id>")
        print("Example: python monitor_journal_experiments.py 25613211")
        sys.exit(1)
    
    job_id = sys.argv[1]
    
    try:
        monitor_experiments(job_id)
    except KeyboardInterrupt:
        print("\n\n⏹️  Monitoring stopped by user")
        print(f"Job {job_id} continues running in background")
        print(f"Resume monitoring: python monitor_journal_experiments.py {job_id}")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
