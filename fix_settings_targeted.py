#!/usr/bin/env python3
"""
Targeted fix for settings.py to add missing dataset configurations
"""

def fix_settings():
    """Add missing dataset configurations to existing entries"""
    
    # Read the file
    with open('settings.py', 'r') as f:
        lines = f.readlines()
    
    # Find and fix specific lines
    for i, line in enumerate(lines):
        # Fix GPT4TS line
        if "'GPT4TS': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.001, 'Weather': 0.0001, 'ECL': 0.0001}," in line:
            lines[i] = "    'GPT4TS': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
        
        # Fix PatchTST line
        elif "'PatchTST': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001}," in line:
            lines[i] = "    'PatchTST': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
        
        # Fix iTransformer line
        elif "'iTransformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.001, 'Weather': 0.00001, 'ECL': 0.0005}," in line:
            lines[i] = "    'iTransformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.001, 'Weather': 0.00001, 'ECL': 0.0005, 'gefcom2014': 0.0005, 'southern_china': 0.0005},\n"
        
        # Fix NLinear line
        elif "'NLinear': {'ETTh2': 0.05, 'ETTm1': 0.05, 'Traffic': 0.005, 'Weather': 0.01, 'ECL': 0.01}," in line:
            lines[i] = "    'NLinear': {'ETTh1': 0.05, 'ETTh2': 0.05, 'ETTm1': 0.05, 'ETTm2': 0.05, 'Traffic': 0.005, 'Weather': 0.01, 'ECL': 0.01, 'gefcom2014': 0.01, 'southern_china': 0.01},\n"
        
        # Add missing models after NLinear
        elif "'NLinear': {'ETTh1': 0.05, 'ETTh2': 0.05, 'ETTm1': 0.05, 'ETTm2': 0.05, 'Traffic': 0.005, 'Weather': 0.01, 'ECL': 0.01, 'gefcom2014': 0.01, 'southern_china': 0.01}," in line:
            # Add missing models
            additional_models = [
                "    'RLinear': {'ETTh1': 0.001, 'ETTh2': 0.001, 'ETTm1': 0.001, 'ETTm2': 0.001, 'Traffic': 0.001, 'Weather': 0.001, 'ECL': 0.001, 'gefcom2014': 0.001, 'southern_china': 0.001},\n",
                "    'Linear': {'ETTh1': 0.001, 'ETTh2': 0.001, 'ETTm1': 0.001, 'ETTm2': 0.001, 'Traffic': 0.001, 'Weather': 0.001, 'ECL': 0.001, 'gefcom2014': 0.001, 'southern_china': 0.001},\n",
                "    'Transformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'Autoformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'Informer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'Crossformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'MTGNN': {'ETTh1': 0.001, 'ETTh2': 0.001, 'ETTm1': 0.001, 'ETTm2': 0.001, 'Traffic': 0.001, 'Weather': 0.001, 'ECL': 0.001, 'gefcom2014': 0.001, 'southern_china': 0.001},\n",
                "    'FSNet': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'OneNet': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'LIFT': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'LightMTS': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n",
                "    'TCN': {'ETTh1': 0.003, 'ETTh2': 0.003, 'ETTm1': 0.001, 'ETTm2': 0.001, 'Traffic': 0.003, 'Weather': 0.001, 'ECL': 0.003, 'gefcom2014': 0.003, 'southern_china': 0.003},\n",
                "    'TCN_RevIN': {'ETTh1': 0.001, 'ETTh2': 0.001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.003, 'Weather': 0.001, 'ECL': 0.003, 'gefcom2014': 0.001, 'southern_china': 0.001},\n"
            ]
            lines[i:i+1] = [line] + additional_models
        
        # Fix existing RevIN entries
        elif "'Informer_RevIN': {'ETTh2': 0.0001, 'ETTm1': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001}," in line:
            lines[i] = "    'Informer_RevIN': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
        
        elif "'Autoformer_RevIN': {'ETTh2': 0.0001, 'ETTm1': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001}," in line:
            lines[i] = "    'Autoformer_RevIN': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
        
        elif "'Informer': {'ETTh2': 0.0001, 'ETTm1': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001}," in line:
            lines[i] = "    'Informer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
        
        elif "'Autoformer': {'ETTh2': 0.0001, 'ETTm1': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001}" in line:
            lines[i] = "    'Autoformer': {'ETTh1': 0.0001, 'ETTh2': 0.0001, 'ETTm1': 0.0001, 'ETTm2': 0.0001, 'Traffic': 0.0001, 'Weather': 0.0001, 'ECL': 0.0001, 'gefcom2014': 0.0001, 'southern_china': 0.0001},\n"
            # Add missing ensemble entries
            additional_ensemble = [
                "    'TCN_Ensemble': {'ETTh1': 0.003, 'ETTh2': 0.003, 'ETTm1': 0.0003, 'ETTm2': 0.0003, 'Traffic': 0.003, 'Weather': 0.001, 'ECL': 0.003, 'gefcom2014': 0.003, 'southern_china': 0.003},\n",
                "    'FSNet_RevIN': {'ETTh1': 0.003, 'ETTh2': 0.003, 'ETTm1': 0.001, 'ETTm2': 0.001, 'Traffic': 0.003, 'Weather': 0.003, 'ECL': 0.003, 'gefcom2014': 0.003, 'southern_china': 0.003}\n"
            ]
            lines[i:i+1] = [line] + additional_ensemble
    
    # Write back
    with open('settings.py', 'w') as f:
        f.writelines(lines)
    
    print("✓ Settings.py updated with missing configurations")

if __name__ == "__main__":
    fix_settings()
