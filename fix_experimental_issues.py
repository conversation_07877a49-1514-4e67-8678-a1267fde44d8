#!/usr/bin/env python3
"""
Comprehensive fix script for experimental issues identified in Job ID: 25610322
This script addresses:
1. Missing learning rate configurations for gefcom2014 and southern_china
2. Incomplete model support in learning rate dictionaries
3. Missing checkpoint dependencies
4. Model compatibility issues
"""

import os
import sys
import json
import shutil
from pathlib import Path

def fix_learning_rate_configurations():
    """Fix missing learning rate configurations in settings.py"""
    print("=== Fixing Learning Rate Configurations ===")
    
    # Read current settings
    with open('settings.py', 'r') as f:
        content = f.read()
    
    # Define all datasets and models
    all_datasets = ['ETTh1', 'ETTh2', 'ETTm1', 'ETTm2', 'ECL', 'Weather', 'Traffic', 'gefcom2014', 'southern_china']
    all_models = [
        'PatchTST', 'iTransformer', 'Autoformer', 'Informer', 'Transformer', 'Crossformer',
        'DLinear', 'Linear', 'NLinear', 'RLinear', 'TCN', 'TCN_RevIN', 'MTGNN',
        'GPT4TS', 'FSNet', 'OneNet', 'LIFT', 'LightMTS'
    ]
    
    # Create comprehensive learning rate dictionaries
    pretrain_lr_online_dict = {}
    pretrain_lr_dict = {}
    
    # Default learning rates by model type
    default_lrs = {
        # Transformer-based models
        'PatchTST': 0.0001, 'iTransformer': 0.0001, 'Autoformer': 0.0001, 
        'Informer': 0.0001, 'Transformer': 0.0001, 'Crossformer': 0.0001,
        'GPT4TS': 0.0001, 'FSNet': 0.0001, 'OneNet': 0.0001, 'LIFT': 0.0001, 'LightMTS': 0.0001,
        # Linear models
        'DLinear': 0.001, 'Linear': 0.001, 'NLinear': 0.01, 'RLinear': 0.001,
        # CNN and Graph models
        'TCN': 0.003, 'TCN_RevIN': 0.001, 'MTGNN': 0.001
    }
    
    # Special learning rates for specific dataset-model combinations
    special_lrs = {
        'iTransformer': {'ECL': 0.0005, 'gefcom2014': 0.0005, 'southern_china': 0.0005},
        'NLinear': {'ETTh2': 0.05, 'ETTm1': 0.05, 'ECL': 0.01, 'gefcom2014': 0.01, 'southern_china': 0.01},
        'TCN': {'ECL': 0.003, 'gefcom2014': 0.003, 'southern_china': 0.003},
        'TCN_RevIN': {'ECL': 0.003, 'ETTm1': 0.0001, 'gefcom2014': 0.001, 'southern_china': 0.001}
    }
    
    # Build comprehensive dictionaries
    for model in all_models:
        pretrain_lr_online_dict[model] = {}
        pretrain_lr_dict[model] = {}
        
        base_lr = default_lrs.get(model, 0.0001)
        
        for dataset in all_datasets:
            # Use special learning rate if available, otherwise use default
            if model in special_lrs and dataset in special_lrs[model]:
                lr = special_lrs[model][dataset]
            else:
                lr = base_lr
            
            pretrain_lr_online_dict[model][dataset] = lr
            pretrain_lr_dict[model][dataset] = lr
    
    # Add RevIN variants
    for model in ['Informer', 'Autoformer']:
        model_revin = f'{model}_RevIN'
        pretrain_lr_online_dict[model_revin] = {}
        for dataset in all_datasets:
            pretrain_lr_online_dict[model_revin][dataset] = 0.0001
    
    # Add ensemble variants
    pretrain_lr_online_dict['TCN_Ensemble'] = {}
    pretrain_lr_online_dict['FSNet_RevIN'] = {}
    for dataset in all_datasets:
        pretrain_lr_online_dict['TCN_Ensemble'][dataset] = 0.003
        pretrain_lr_online_dict['FSNet_RevIN'][dataset] = 0.003
    
    # Generate new settings content
    new_pretrain_lr_online = "pretrain_lr_online_dict = {\n"
    for model, dataset_lrs in sorted(pretrain_lr_online_dict.items()):
        new_pretrain_lr_online += f"    '{model}': {{"
        dataset_items = []
        for dataset, lr in sorted(dataset_lrs.items()):
            dataset_items.append(f"'{dataset}': {lr}")
        new_pretrain_lr_online += ", ".join(dataset_items)
        new_pretrain_lr_online += "},\n"
    new_pretrain_lr_online += "}\n"
    
    new_pretrain_lr = "pretrain_lr_dict = {\n"
    for model, dataset_lrs in sorted(pretrain_lr_dict.items()):
        new_pretrain_lr += f"    '{model}': {{"
        dataset_items = []
        for dataset, lr in sorted(dataset_lrs.items()):
            dataset_items.append(f"'{dataset}': {lr}")
        new_pretrain_lr += ", ".join(dataset_items)
        new_pretrain_lr += "},\n"
    new_pretrain_lr += "}\n"
    
    # Replace in content
    import re
    
    # Replace pretrain_lr_online_dict
    pattern1 = r'pretrain_lr_online_dict = \{[^}]*\}'
    content = re.sub(pattern1, new_pretrain_lr_online.strip(), content, flags=re.DOTALL)
    
    # Replace pretrain_lr_dict
    pattern2 = r'pretrain_lr_dict = \{[^}]*\}'
    content = re.sub(pattern2, new_pretrain_lr.strip(), content, flags=re.DOTALL)
    
    # Write back to file
    with open('settings.py', 'w') as f:
        f.write(content)
    
    print("✓ Learning rate configurations updated")

def check_model_implementations():
    """Check if all model implementations are available and working"""
    print("\n=== Checking Model Implementations ===")
    
    models_dir = Path('models')
    expected_models = [
        'PatchTST.py', 'iTransformer.py', 'Autoformer.py', 'Informer.py', 
        'Transformer.py', 'Crossformer.py', 'DLinear.py', 'Linear.py', 
        'NLinear.py', 'RLinear.py', 'TCN.py', 'TCN_RevIN.py', 'MTGNN.py',
        'GPT4TS.py', 'FSNet.py', 'OneNet.py', 'LIFT.py', 'LightMTS.py'
    ]
    
    missing_models = []
    for model_file in expected_models:
        if not (models_dir / model_file).exists():
            missing_models.append(model_file)
    
    if missing_models:
        print(f"❌ Missing model files: {missing_models}")
        return False
    else:
        print("✓ All model files present")
        return True

def check_experiment_classes():
    """Check if all experiment classes are available"""
    print("\n=== Checking Experiment Classes ===")
    
    exp_dir = Path('exp')
    expected_exp_files = [
        'exp_basic.py', 'exp_main.py', 'exp_online.py', 
        'exp_clear_e.py', 'exp_proceed.py', 'exp_solid.py'
    ]
    
    missing_exp = []
    for exp_file in expected_exp_files:
        if not (exp_dir / exp_file).exists():
            missing_exp.append(exp_file)
    
    if missing_exp:
        print(f"❌ Missing experiment files: {missing_exp}")
        return False
    else:
        print("✓ All experiment files present")
        return True

def create_test_script():
    """Create a comprehensive test script to validate fixes"""
    print("\n=== Creating Test Script ===")
    
    test_script = '''#!/usr/bin/env python3
"""
Test script to validate experimental setup fixes
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_provider.data_factory import data_provider
from settings import data_settings, pretrain_lr_online_dict, pretrain_lr_dict

def test_dataset_loading():
    """Test that all datasets can be loaded"""
    print("=== Testing Dataset Loading ===")
    datasets = ['ECL', 'gefcom2014', 'southern_china']
    
    class Args:
        def __init__(self, dataset_name):
            config = data_settings[dataset_name]
            self.data = dataset_name
            self.dataset = dataset_name
            self.data_path = config['data']
            self.root_path = './dataset/'
            self.features = 'M'
            self.target = config['T']
            self.seq_len = 96
            self.label_len = 48
            self.pred_len = 24
            self.timeenc = 1
            self.freq = 'h'
            self.train_only = False
            self.pin_gpu = False
            self.wrap_data_class = []
            self.model = 'PatchTST'
            self.embed = 'timeF'
            self.use_time = False
            self.batch_size = 16
            self.num_workers = 0
            self.local_rank = -1
            self.borders = None
    
    for dataset in datasets:
        try:
            args = Args(dataset)
            train_data, train_loader = data_provider(args, flag='train')
            print(f"✓ {dataset}: {len(train_data)} samples")
        except Exception as e:
            print(f"❌ {dataset}: {str(e)}")

def test_learning_rate_coverage():
    """Test that all models have learning rates for all datasets"""
    print("\\n=== Testing Learning Rate Coverage ===")
    
    datasets = ['ETTh1', 'ETTh2', 'ETTm1', 'ETTm2', 'ECL', 'gefcom2014', 'southern_china']
    models = ['PatchTST', 'iTransformer', 'DLinear', 'Linear', 'NLinear', 'RLinear']
    
    missing_configs = []
    for model in models:
        if model in pretrain_lr_online_dict:
            for dataset in datasets:
                if dataset not in pretrain_lr_online_dict[model]:
                    missing_configs.append(f"{model}-{dataset}")
    
    if missing_configs:
        print(f"❌ Missing configurations: {missing_configs}")
    else:
        print("✓ All model-dataset combinations have learning rates")

if __name__ == "__main__":
    test_dataset_loading()
    test_learning_rate_coverage()
    print("\\n=== Test Complete ===")
'''
    
    with open('test_fixes.py', 'w') as f:
        f.write(test_script)
    
    os.chmod('test_fixes.py', 0o755)
    print("✓ Test script created: test_fixes.py")

def main():
    """Main fix execution"""
    print("🔧 COMPREHENSIVE EXPERIMENTAL FIXES")
    print("=" * 50)
    
    # Step 1: Fix learning rate configurations
    fix_learning_rate_configurations()
    
    # Step 2: Check model implementations
    models_ok = check_model_implementations()
    
    # Step 3: Check experiment classes
    exp_ok = check_experiment_classes()
    
    # Step 4: Create test script
    create_test_script()
    
    print("\n" + "=" * 50)
    if models_ok and exp_ok:
        print("✅ ALL FIXES APPLIED SUCCESSFULLY")
        print("Next steps:")
        print("1. Run: python test_fixes.py")
        print("2. If tests pass, run comprehensive experiments")
        print("3. Target: >90% success rate for 1,134 experiments")
    else:
        print("⚠️  SOME ISSUES REMAIN")
        print("Please address missing files before proceeding")

if __name__ == "__main__":
    main()
