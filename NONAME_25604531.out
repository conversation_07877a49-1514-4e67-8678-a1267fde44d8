Modules Release 5.3.0 (2023-05-14)
DCC-SW: Added modules (2023-aug/XeonGold6126)
DCC-SW: Removed standard modules: /apps/dcc/etc/ModulesAlma92/modulefiles
DCC-SW: Using gnu/12.3.0 compiler (requested)
/bin/bash: line 1: run_clear_e_experiments.sh: command not found

------------------------------------------------------------
Sender: LSF System <<EMAIL>>
Subject: Job 25604531: <NONAME> in cluster <dcc> Exited

Job <NONAME> was submitted from host <hpclogin2> by user <xiuli> in cluster <dcc> at Tue Jul 15 17:05:16 2025
Job was executed on host(s) <n-62-31-21>, in queue <hpc>, as user <xiuli> in cluster <dcc> at Tue Jul 15 17:05:16 2025
</zhome/bb/9/101964> was used as the home directory.
</zhome/bb/9/101964/xiuli/CLEAR-E> was used as the working directory.
Started at Tue Jul 15 17:05:16 2025
Terminated at Tue Jul 15 17:05:24 2025
Results reported at Tue Jul 15 17:05:24 2025

Your job looked like:

------------------------------------------------------------
# LSBATCH: User input
run_clear_e_experiments.sh quick
------------------------------------------------------------

Exited with exit code 127.

Resource usage summary:

    CPU time :                                   3.60 sec.
    Max Memory :                                 -
    Average Memory :                             -
    Total Requested Memory :                     1024.00 MB
    Delta Memory :                               -
    Max Swap :                                   -
    Max Processes :                              -
    Max Threads :                                -
    Run time :                                   27 sec.
    Turnaround time :                            8 sec.

The output (if any) is above this job summary.

